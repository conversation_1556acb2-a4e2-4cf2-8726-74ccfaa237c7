@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');



/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-in;
}

/* Custom gradient text */
/* .gradient-text {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
} */

/* Custom hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Loading spinner */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Custom input validation styles */
.input-error {
  border-color: #ef4444 !important;
}

.input-error:focus {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}





.gradient-text {
  background: linear-gradient(90deg,rgba(82, 32, 3, 1) 0%, rgba(153, 109, 8, 1) 35%, rgba(255, 64, 0, 1) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
        
        .email-mockup {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .pulse-slow {
            animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .animate-slide-up {
            animation: slideInUp 0.6s ease-out;
        }


        .third-hero-btn {
            background: linear-gradient(135deg, #6F00FF 0%, #2A0C6E 100%);
            border-color: #6F00FF;
        }

        .third-hero-btn:hover {
            background: linear-gradient(135deg, #2A0C6E 0%, #6F00FF 100%);
            border-color: #2A0C6E;
        }
        
        .hormozi-gradient-text {
          background: linear-gradient(135deg, #6F00FF 0%, #2A0C6E 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

.poppins {
  font-family: 'Poppins', sans-serif;
}



.input-underline:focus {
  border-bottom-color: var(--fallback-p, oklch(var(--p)/var(--tw-border-opacity))) !important;
}

.input-underline::placeholder {
  color: var(--fallback-bc, oklch(var(--bc)/0.4));
}

/* Form step transitions */
.form-step {
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.form-step.active {
  display: block;
  opacity: 1;
}

/* Custom step styling */
.steps .step::before {
  transition: all 0.3s ease;
}

.steps .step-primary::before {
  background-color: #d97757;
  border-color: #d97757;
}

/* Theme transition */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--fallback-b2, oklch(var(--b2)/var(--tw-bg-opacity)));
}

::-webkit-scrollbar-thumb {
  background: var(--fallback-bc, oklch(var(--bc)/0.2));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--fallback-bc, oklch(var(--bc)/0.3));
}

/* Radio button hover effects */
.form-control label:hover {
  background-color: var(--fallback-b2, oklch(var(--b2)/var(--tw-bg-opacity)));
}

/* Custom radio styling */
.radio:checked {
  background-color: var(--fallback-p, oklch(var(--p)/var(--tw-bg-opacity)));
  border-color: var(--fallback-p, oklch(var(--p)/var(--tw-border-opacity)));
}

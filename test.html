
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@5" rel="stylesheet" type="text/css" />
<script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
<style>
    @import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-in;
}

/* Custom gradient text */
.gradient-text {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Loading spinner */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Custom input validation styles */
.input-error {
  border-color: #ef4444 !important;
}

.input-error:focus {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}
</style>
</head>
<body>
    
<div class="min-h-screen bg-base-200">
      <div class="container mx-auto px-6 py-8">
        <div class="mb-8">
          <a href="#landing" class="btn btn-ghost gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Home
          </a>
        </div>
        
        <div id="user-section" class="max-w-2xl mx-auto">
          <div class="card bg-base-100 shadow-2xl">
            <div class="card-body p-8">
              <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-base-content mb-4">Get Started</h1>
                <p class="text-lg text-base-content/70">First, we need some basic information about you</p>
              </div>
              
              <form id="user-form" class="space-y-6">
                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-semibold">Full Name</span>
                  </label>
                  <input type="text" id="name" name="name" class="input input-bordered input-lg" required>
                  <div id="name-error" class="label hidden">
                    <span class="label-text-alt text-error"></span>
                  </div>
                </div>
                
                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-semibold">LinkedIn Profile Link</span>
                  </label>
                  <input type="url" id="linkedin" name="linkedin" class="input input-bordered input-lg" placeholder="https://linkedin.com/in/your-profile" required>
                  <div id="linkedin-error" class="label hidden">
                    <span class="label-text-alt text-error"></span>
                  </div>
                </div>
                
                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-semibold">Business Email</span>
                  </label>
                  <input type="email" id="email" name="email" class="input input-bordered input-lg" required>
                  <div id="email-error" class="label hidden">
                    <span class="label-text-alt text-error"></span>
                  </div>
                  <div class="label">
                    <span class="label-text-alt text-base-content/60">Gmail addresses are not accepted for business use</span>
                  </div>
                </div>
                
                <button type="submit" class="btn btn-primary btn-lg w-full">
                  Continue
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </button>
              </form>
            </div>
          </div>
        </div>

        <div id="ai-section" class="max-w-3xl mx-auto hidden">
          <div class="card bg-base-100 shadow-2xl">
            <div class="card-body p-8">
              <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-base-content mb-4">Generate Hormozi-style headlines and subjects for your cold outreach!</h1>
                <div class="alert alert-warning">
                  <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <span class="font-semibold">⚠️ You only got one shot, use it wisely!</span>
                </div>
              </div>
              
              <form id="ai-form" class="space-y-6">
                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-semibold">Email Content</span>
                  </label>
                  <textarea id="email_content" name="email_content" class="textarea textarea-bordered textarea-lg h-32" placeholder="INSERT EMAIL BODY HERE" required></textarea>
                </div>
                
                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-semibold">Target Audience</span>
                  </label>
                  <input type="text" id="target_audience" name="target_audience" class="input input-bordered input-lg" placeholder="e.g., B2B SaaS founders, 51–200 employees" required>
                </div>
                
                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-semibold">Tone of Voice</span>
                  </label>
                  <input type="text" id="tone_of_voice" name="tone_of_voice" class="input input-bordered input-lg" placeholder="e.g., like Hormozi meets cold email pro" required>
                </div>
                
                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-semibold">Main Pain Point</span>
                  </label>
                  <textarea id="main_pain" name="main_pain" class="textarea textarea-bordered textarea-lg h-24" placeholder="Manual cold outreach is time-consuming, lack of personalization, doesn't scale" required></textarea>
                </div>
                
                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-semibold">Outcome We Promise</span>
                  </label>
                  <input type="text" id="outcome" name="outcome" class="input input-bordered input-lg" placeholder="Get more replies and convert more." required>
                </div>
                
                <button type="submit" class="btn btn-primary btn-lg w-full">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  Generate Headlines
                </button>
              </form>
            </div>
          </div>
        </div>

        <div id="loader-section" class="max-w-2xl mx-auto hidden">
          <div class="card bg-base-100 shadow-2xl">
            <div class="card-body p-12 text-center">
              <div class="loading-spinner mx-auto mb-8"></div>
              <h2 class="text-3xl font-bold text-base-content mb-4">Bear with me for a minute...</h2>
              <p class="text-lg text-base-content/70 leading-relaxed">I'm not a normal AI, and I focus on results and it will be valuable for you, so I take time to think!</p>
            </div>
          </div>
        </div>

        <div id="result-section" class="max-w-4xl mx-auto hidden">
          <!-- Results will be populated here -->
        </div>
      </div>
    </div>
</body>
</html>

